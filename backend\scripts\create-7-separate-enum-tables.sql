-- =====================================================
-- CREATE 7 SEPARATE ENUM TABLES
-- =====================================================
-- Creates individual tables for each enum type as requested

-- 1. SPECIALTIES TABLE
CREATE TABLE IF NOT EXISTS specialties (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTA<PERSON> DEFAULT CURRENT_TIMESTAMP
);

-- 2. DEPARTMENTS TABLE  
CREATE TABLE IF NOT EXISTS departments (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. ROOM_TYPES TABLE
CREATE TABLE IF NOT EXISTS room_types (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 4. DIAGNOSIS TABLE
CREATE TABLE IF NOT EXISTS diagnosis (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  icd_code VARCHAR(20), -- International Classification of Diseases
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 5. MEDICATIONS TABLE
CREATE TABLE IF NOT EXISTS medications (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  drug_class VARCHAR(100), -- Nhóm thuốc
  dosage_form VARCHAR(50), -- Dạng bào chế
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 6. STATUS_VALUES TABLE
CREATE TABLE IF NOT EXISTS status_values (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  applies_to VARCHAR(100), -- Áp dụng cho bảng nào (doctors, patients, appointments, etc.)
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 7. PAYMENT_METHODS TABLE
CREATE TABLE IF NOT EXISTS payment_methods (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  requires_verification BOOLEAN DEFAULT false,
  processing_fee DECIMAL(5,2) DEFAULT 0.00,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_specialties_active ON specialties(is_active);
CREATE INDEX IF NOT EXISTS idx_specialties_code ON specialties(code);
CREATE INDEX IF NOT EXISTS idx_departments_active ON departments(is_active);
CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code);
CREATE INDEX IF NOT EXISTS idx_room_types_active ON room_types(is_active);
CREATE INDEX IF NOT EXISTS idx_room_types_code ON room_types(code);
CREATE INDEX IF NOT EXISTS idx_diagnosis_active ON diagnosis(is_active);
CREATE INDEX IF NOT EXISTS idx_diagnosis_code ON diagnosis(code);
CREATE INDEX IF NOT EXISTS idx_medications_active ON medications(is_active);
CREATE INDEX IF NOT EXISTS idx_medications_code ON medications(code);
CREATE INDEX IF NOT EXISTS idx_status_values_active ON status_values(is_active);
CREATE INDEX IF NOT EXISTS idx_status_values_code ON status_values(code);
CREATE INDEX IF NOT EXISTS idx_payment_methods_active ON payment_methods(is_active);
CREATE INDEX IF NOT EXISTS idx_payment_methods_code ON payment_methods(code);

-- Grant permissions
GRANT ALL ON specialties TO authenticated, anon, service_role;
GRANT ALL ON departments TO authenticated, anon, service_role;
GRANT ALL ON room_types TO authenticated, anon, service_role;
GRANT ALL ON diagnosis TO authenticated, anon, service_role;
GRANT ALL ON medications TO authenticated, anon, service_role;
GRANT ALL ON status_values TO authenticated, anon, service_role;
GRANT ALL ON payment_methods TO authenticated, anon, service_role;

#!/usr/bin/env node

/**
 * Complete Dynamic Enum System Deployment
 * <PERSON><PERSON><PERSON> thiện triển khai hệ thống enum động
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseKey);

async function executeSQL(filename) {
  const filePath = path.join(__dirname, filename);
  const sql = fs.readFileSync(filePath, 'utf8');
  
  console.log(`📄 Executing ${filename}...`);
  
  const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
  
  if (error) {
    console.error(`❌ Error executing ${filename}:`, error);
    throw error;
  }
  
  console.log(`✅ ${filename} executed successfully`);
  return data;
}

async function completeDynamicEnumDeployment() {
  console.log('🚀 Starting Complete Dynamic Enum System Deployment...\n');

  try {
    // Step 1: Fix missing functions
    console.log('📋 Step 1: Creating missing validation functions...');
    await executeSQL('fix-missing-functions.sql');
    console.log('✅ Validation functions created successfully\n');

    // Step 2: Fix ROOM_TYPES missing data
    console.log('📋 Step 2: Adding missing ROOM_TYPES data...');
    await executeSQL('fix-room-types-data.sql');
    console.log('✅ ROOM_TYPES data added successfully\n');

    // Step 3: Run migration to update existing tables
    console.log('📋 Step 3: Migrating existing tables to use dynamic enums...');
    await executeSQL('migrate-to-dynamic-enums.sql');
    console.log('✅ Table migration completed successfully\n');

    // Step 4: Test the system
    console.log('📋 Step 4: Testing dynamic enum system...');
    
    // Test validation function
    const { data: testResult, error: testError } = await supabase
      .rpc('validate_dynamic_enum_value', {
        value_to_check: 'active',
        category_id: 'STATUS_VALUES'
      });

    if (testError) {
      console.log('⚠️  Validation function test failed:', testError.message);
    } else {
      console.log(`✅ Validation function test passed: ${testResult}`);
    }

    // Test enum data
    const { data: enumData, error: enumError } = await supabase
      .from('system_enums')
      .select('category_id, count(*)')
      .eq('is_active', true);

    if (enumError) {
      console.log('⚠️  Enum data test failed:', enumError.message);
    } else {
      console.log('✅ Enum data test passed');
    }

    console.log('\n🎉 Dynamic Enum System Deployment Completed Successfully!');
    console.log('\n📊 Summary:');
    console.log('✅ Validation functions created');
    console.log('✅ ROOM_TYPES data added');
    console.log('✅ Table migrations applied');
    console.log('✅ System tested and working');
    
    console.log('\n🔧 Next steps:');
    console.log('1. Update frontend components to use dynamic enums');
    console.log('2. Test all forms and dropdowns');
    console.log('3. Verify data integrity');

  } catch (error) {
    console.error('\n❌ Deployment failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check Supabase connection');
    console.log('2. Verify environment variables');
    console.log('3. Check database permissions');
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  completeDynamicEnumDeployment();
}

module.exports = { completeDynamicEnumDeployment };

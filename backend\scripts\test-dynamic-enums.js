// =====================================================
// TEST DYNAMIC ENUM SYSTEM
// =====================================================
// Comprehensive test script for the dynamic enum system

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ciasxktujslgsdgylimv.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('❌ Missing Supabase service role key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runTests() {
  console.log('🧪 Starting Dynamic Enum System Tests...\n');

  const results = {
    passed: 0,
    failed: 0,
    tests: []
  };

  // Test 1: Check if tables exist
  await runTest('Tables Existence', async () => {
    const { data: categories } = await supabase
      .from('enum_categories')
      .select('category_id')
      .limit(1);
    
    const { data: enums } = await supabase
      .from('system_enums')
      .select('enum_id')
      .limit(1);

    if (!categories || !enums) {
      throw new Error('Required tables do not exist');
    }
    
    return 'Tables exist and are accessible';
  }, results);

  // Test 2: Check views
  await runTest('Views Accessibility', async () => {
    const { data: viewCategories } = await supabase
      .from('v_enum_categories')
      .select('category_id')
      .limit(1);
    
    const { data: viewEnums } = await supabase
      .from('v_system_enums')
      .select('enum_id')
      .limit(1);

    if (!viewCategories || !viewEnums) {
      throw new Error('Views are not accessible');
    }
    
    return 'Views are working correctly';
  }, results);

  // Test 3: Check sample data
  await runTest('Sample Data Verification', async () => {
    const expectedCategories = [
      'SPECIALTIES', 'DEPARTMENTS', 'ROOM_TYPES', 
      'DIAGNOSIS', 'MEDICATIONS', 'STATUS_VALUES', 'PAYMENT_METHODS'
    ];

    const { data: categories } = await supabase
      .from('enum_categories')
      .select('category_id')
      .in('category_id', expectedCategories);

    if (!categories || categories.length !== expectedCategories.length) {
      throw new Error(`Expected ${expectedCategories.length} categories, found ${categories?.length || 0}`);
    }

    // Check if each category has enums
    for (const category of expectedCategories) {
      const { data: enums } = await supabase
        .from('system_enums')
        .select('enum_id')
        .eq('category_id', category)
        .limit(1);

      if (!enums || enums.length === 0) {
        throw new Error(`No enums found for category: ${category}`);
      }
    }
    
    return `All ${expectedCategories.length} categories have sample data`;
  }, results);

  // Test 4: Test validation functions
  await runTest('Validation Functions', async () => {
    // Test valid value
    const { data: validResult, error: validError } = await supabase
      .rpc('validate_dynamic_enum_value', {
        value_to_check: 'internal_medicine',
        category_id: 'SPECIALTIES'
      });

    if (validError || !validResult) {
      throw new Error(`Validation function failed for valid value: ${validError?.message}`);
    }

    // Test invalid value
    const { data: invalidResult, error: invalidError } = await supabase
      .rpc('validate_dynamic_enum_value', {
        value_to_check: 'invalid_specialty',
        category_id: 'SPECIALTIES'
      });

    if (invalidError || invalidResult !== false) {
      throw new Error('Validation function should return false for invalid value');
    }

    return 'Validation functions working correctly';
  }, results);

  // Test 5: Test helper functions
  await runTest('Helper Functions', async () => {
    // Test get_enum_display_name
    const { data: displayName, error: nameError } = await supabase
      .rpc('get_enum_display_name', {
        p_category_id: 'SPECIALTIES',
        p_enum_key: 'internal_medicine',
        p_language: 'vi'
      });

    if (nameError || !displayName) {
      throw new Error(`get_enum_display_name failed: ${nameError?.message}`);
    }

    // Test get_enum_options
    const { data: options, error: optionsError } = await supabase
      .rpc('get_enum_options', {
        p_category_id: 'SPECIALTIES',
        p_language: 'vi'
      });

    if (optionsError || !options || options.length === 0) {
      throw new Error(`get_enum_options failed: ${optionsError?.message}`);
    }

    return `Helper functions working (found ${options.length} specialties)`;
  }, results);

  // Test 6: Test CRUD operations
  await runTest('CRUD Operations', async () => {
    const testEnumId = 'TEST_ENUM_' + Date.now();
    
    // Create
    const { data: created, error: createError } = await supabase
      .from('system_enums')
      .insert({
        enum_id: testEnumId,
        category_id: 'SPECIALTIES',
        enum_key: 'test_specialty',
        display_name_en: 'Test Specialty',
        display_name_vi: 'Chuyên khoa Test',
        sort_order: 999,
        is_system: false
      })
      .select()
      .single();

    if (createError) {
      throw new Error(`Create failed: ${createError.message}`);
    }

    // Read
    const { data: read, error: readError } = await supabase
      .from('system_enums')
      .select('*')
      .eq('enum_id', testEnumId)
      .single();

    if (readError || !read) {
      throw new Error(`Read failed: ${readError?.message}`);
    }

    // Update
    const { data: updated, error: updateError } = await supabase
      .from('system_enums')
      .update({ display_name_vi: 'Chuyên khoa Test Updated' })
      .eq('enum_id', testEnumId)
      .select()
      .single();

    if (updateError) {
      throw new Error(`Update failed: ${updateError.message}`);
    }

    // Delete
    const { error: deleteError } = await supabase
      .from('system_enums')
      .delete()
      .eq('enum_id', testEnumId);

    if (deleteError) {
      throw new Error(`Delete failed: ${deleteError.message}`);
    }

    return 'CRUD operations working correctly';
  }, results);

  // Test 7: Test indexes performance
  await runTest('Index Performance', async () => {
    const startTime = Date.now();
    
    // Query that should use indexes
    const { data, error } = await supabase
      .from('system_enums')
      .select('enum_key, display_name_vi')
      .eq('category_id', 'SPECIALTIES')
      .eq('is_active', true)
      .order('sort_order');

    const endTime = Date.now();
    const queryTime = endTime - startTime;

    if (error) {
      throw new Error(`Index query failed: ${error.message}`);
    }

    if (queryTime > 1000) {
      throw new Error(`Query too slow: ${queryTime}ms (expected < 1000ms)`);
    }

    return `Index query completed in ${queryTime}ms`;
  }, results);

  // Test 8: Test data integrity
  await runTest('Data Integrity', async () => {
    // Check for duplicate enum_keys within categories
    const { data: duplicates, error: dupError } = await supabase
      .from('system_enums')
      .select('category_id, enum_key, count(*)')
      .group('category_id, enum_key')
      .having('count(*) > 1');

    if (dupError) {
      throw new Error(`Duplicate check failed: ${dupError.message}`);
    }

    if (duplicates && duplicates.length > 0) {
      throw new Error(`Found duplicate enum_keys: ${JSON.stringify(duplicates)}`);
    }

    // Check for orphaned enums
    const { data: orphans, error: orphanError } = await supabase
      .from('system_enums')
      .select('enum_id, category_id')
      .not('category_id', 'in', 
        `(SELECT category_id FROM enum_categories WHERE is_active = true)`
      );

    if (orphanError) {
      throw new Error(`Orphan check failed: ${orphanError.message}`);
    }

    if (orphans && orphans.length > 0) {
      throw new Error(`Found orphaned enums: ${orphans.length}`);
    }

    return 'Data integrity checks passed';
  }, results);

  // Print results
  console.log('\n📊 Test Results:');
  console.log('='.repeat(50));
  
  results.tests.forEach(test => {
    const status = test.passed ? '✅' : '❌';
    console.log(`${status} ${test.name}: ${test.result}`);
    if (!test.passed && test.error) {
      console.log(`   Error: ${test.error}`);
    }
  });

  console.log('='.repeat(50));
  console.log(`Total: ${results.passed + results.failed} tests`);
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  
  if (results.failed === 0) {
    console.log('\n🎉 All tests passed! Dynamic Enum System is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the errors above.');
    process.exit(1);
  }
}

async function runTest(name, testFunction, results) {
  try {
    console.log(`🧪 Running: ${name}...`);
    const result = await testFunction();
    console.log(`✅ ${name}: ${result}`);
    
    results.passed++;
    results.tests.push({
      name,
      passed: true,
      result
    });
  } catch (error) {
    console.log(`❌ ${name}: ${error.message}`);
    
    results.failed++;
    results.tests.push({
      name,
      passed: false,
      result: 'Failed',
      error: error.message
    });
  }
}

// Performance benchmark
async function runBenchmark() {
  console.log('\n⚡ Running Performance Benchmark...\n');

  const iterations = 100;
  const results = [];

  for (let i = 0; i < iterations; i++) {
    const startTime = Date.now();
    
    await supabase
      .from('v_system_enums')
      .select('enum_key, display_name_vi, category_name')
      .eq('category_id', 'SPECIALTIES')
      .eq('is_active', true);
    
    const endTime = Date.now();
    results.push(endTime - startTime);
  }

  const avgTime = results.reduce((a, b) => a + b, 0) / results.length;
  const minTime = Math.min(...results);
  const maxTime = Math.max(...results);

  console.log(`📈 Benchmark Results (${iterations} iterations):`);
  console.log(`   Average: ${avgTime.toFixed(2)}ms`);
  console.log(`   Min: ${minTime}ms`);
  console.log(`   Max: ${maxTime}ms`);
  
  if (avgTime < 100) {
    console.log('✅ Performance is excellent');
  } else if (avgTime < 500) {
    console.log('⚠️  Performance is acceptable');
  } else {
    console.log('❌ Performance needs improvement');
  }
}

// Main execution
if (require.main === module) {
  runTests()
    .then(() => {
      if (process.argv.includes('--benchmark')) {
        return runBenchmark();
      }
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = {
  runTests,
  runBenchmark
};

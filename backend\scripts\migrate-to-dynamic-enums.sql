-- =====================================================
-- MIGRATE EXISTING TABLES TO USE DYNAMIC ENUMS
-- =====================================================
-- Updates existing table constraints to use dynamic enum validation

-- 1. Drop old static enum validation constraints
-- Note: Run this carefully and check existing data first

-- Drop old validation functions if they exist
DROP FUNCTION IF EXISTS validate_enum_value CASCADE;
DROP FUNCTION IF EXISTS validate_enum_value_nullable CASCADE;

-- 2. Update doctors table to use dynamic enums
-- Remove old constraints
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_valid_specialization;
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_valid_qualification;
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_valid_status;

-- Add new dynamic enum constraints for doctors
ALTER TABLE doctors 
ADD CONSTRAINT check_dynamic_specialization 
CHECK (validate_dynamic_enum_value(specialization, 'SPECIALTIES'));

ALTER TABLE doctors 
ADD CONSTRAINT check_dynamic_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

-- 3. Update patients table to use dynamic enums
-- Remove old constraints
ALTER TABLE patients DROP CONSTRAINT IF EXISTS check_valid_gender;
ALTER TABLE patients DROP CONSTRAINT IF EXISTS check_valid_blood_type;
ALTER TABLE patients DROP CONSTRAINT IF EXISTS check_valid_status;

-- Add new dynamic enum constraints for patients
ALTER TABLE patients 
ADD CONSTRAINT check_dynamic_gender 
CHECK (validate_dynamic_enum_value_nullable(gender, 'GENDER'));

ALTER TABLE patients 
ADD CONSTRAINT check_dynamic_blood_type 
CHECK (validate_dynamic_enum_value_nullable(blood_type, 'BLOOD_TYPE'));

ALTER TABLE patients 
ADD CONSTRAINT check_dynamic_patient_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

-- 4. Update appointments table to use dynamic enums
-- Remove old constraints
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS check_valid_status;
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS check_valid_type;

-- Add new dynamic enum constraints for appointments
ALTER TABLE appointments 
ADD CONSTRAINT check_dynamic_appointment_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

ALTER TABLE appointments 
ADD CONSTRAINT check_dynamic_appointment_type 
CHECK (validate_dynamic_enum_value_nullable(type, 'APPOINTMENT_TYPE'));

-- 5. Update rooms table to use dynamic enums
-- Remove old constraints
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS check_valid_room_type;
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS check_valid_room_status;

-- Add new dynamic enum constraints for rooms
ALTER TABLE rooms 
ADD CONSTRAINT check_dynamic_room_type 
CHECK (validate_dynamic_enum_value(room_type, 'ROOM_TYPES'));

ALTER TABLE rooms 
ADD CONSTRAINT check_dynamic_room_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

-- 6. Update departments table to use dynamic enums
-- Add department_type column if it doesn't exist
ALTER TABLE departments 
ADD COLUMN IF NOT EXISTS department_type VARCHAR(50);

-- Add constraint for department type
ALTER TABLE departments 
ADD CONSTRAINT check_dynamic_department_type 
CHECK (validate_dynamic_enum_value_nullable(department_type, 'DEPARTMENTS'));

-- 7. Update medical_records table to use dynamic enums
-- Remove old constraints
ALTER TABLE medical_records DROP CONSTRAINT IF EXISTS check_valid_status;

-- Add new dynamic enum constraints for medical records
ALTER TABLE medical_records 
ADD CONSTRAINT check_dynamic_medical_record_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

-- Add diagnosis column if it doesn't exist
ALTER TABLE medical_records 
ADD COLUMN IF NOT EXISTS primary_diagnosis VARCHAR(100);

ALTER TABLE medical_records 
ADD CONSTRAINT check_dynamic_diagnosis 
CHECK (validate_dynamic_enum_value_nullable(primary_diagnosis, 'DIAGNOSIS'));

-- 8. Update prescriptions table to use dynamic enums
-- Remove old constraints
ALTER TABLE prescriptions DROP CONSTRAINT IF EXISTS check_valid_status;

-- Add new dynamic enum constraints for prescriptions
ALTER TABLE prescriptions 
ADD CONSTRAINT check_dynamic_prescription_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

-- Add medication_type column if it doesn't exist
ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS medication_type VARCHAR(50);

ALTER TABLE prescriptions 
ADD CONSTRAINT check_dynamic_medication_type 
CHECK (validate_dynamic_enum_value_nullable(medication_type, 'MEDICATIONS'));

-- 9. Update billing table to use dynamic enums
-- Add payment_method column if it doesn't exist
ALTER TABLE billing 
ADD COLUMN IF NOT EXISTS payment_method VARCHAR(50);

ALTER TABLE billing 
ADD CONSTRAINT check_dynamic_payment_method 
CHECK (validate_dynamic_enum_value_nullable(payment_method, 'PAYMENT_METHODS'));

-- Add billing status if it doesn't exist
ALTER TABLE billing 
ADD COLUMN IF NOT EXISTS status VARCHAR(50) DEFAULT 'pending';

ALTER TABLE billing 
ADD CONSTRAINT check_dynamic_billing_status 
CHECK (validate_dynamic_enum_value(status, 'STATUS_VALUES'));

-- 10. Update profiles table to use dynamic enums
-- Remove old constraints
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS check_valid_role;
ALTER TABLE profiles DROP CONSTRAINT IF EXISTS check_valid_gender;

-- Add new dynamic enum constraints for profiles
ALTER TABLE profiles 
ADD CONSTRAINT check_dynamic_role 
CHECK (validate_dynamic_enum_value(role, 'USER_ROLES'));

ALTER TABLE profiles 
ADD CONSTRAINT check_dynamic_profile_gender 
CHECK (validate_dynamic_enum_value_nullable(gender, 'GENDER'));

-- 11. Create additional enum categories for existing data
INSERT INTO enum_categories (category_id, category_name, display_name_en, display_name_vi, description_en, description_vi, is_system) VALUES
('USER_ROLES', 'User Roles', 'User Roles', 'Vai trò Người dùng', 'System user roles', 'Các vai trò người dùng hệ thống', true),
('GENDER', 'Gender', 'Gender', 'Giới tính', 'Gender options', 'Các lựa chọn giới tính', true),
('BLOOD_TYPE', 'Blood Types', 'Blood Types', 'Nhóm máu', 'Blood type classifications', 'Phân loại nhóm máu', true),
('APPOINTMENT_TYPE', 'Appointment Types', 'Appointment Types', 'Loại Cuộc hẹn', 'Types of medical appointments', 'Các loại cuộc hẹn y tế', true)
ON CONFLICT (category_id) DO NOTHING;

-- 12. Insert basic enum values for new categories
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, sort_order, color_code, icon_name, is_system) VALUES
-- User Roles
('ROLE001', 'USER_ROLES', 'admin', 'Administrator', 'Quản trị viên', 1, '#DC2626', 'shield', true),
('ROLE002', 'USER_ROLES', 'doctor', 'Doctor', 'Bác sĩ', 2, '#3B82F6', 'stethoscope', true),
('ROLE003', 'USER_ROLES', 'patient', 'Patient', 'Bệnh nhân', 3, '#10B981', 'user', true),
('ROLE004', 'USER_ROLES', 'nurse', 'Nurse', 'Y tá', 4, '#F59E0B', 'heart', true),
('ROLE005', 'USER_ROLES', 'receptionist', 'Receptionist', 'Lễ tân', 5, '#8B5CF6', 'phone', true),

-- Gender
('GEN001', 'GENDER', 'male', 'Male', 'Nam', 1, '#3B82F6', 'male', true),
('GEN002', 'GENDER', 'female', 'Female', 'Nữ', 2, '#EC4899', 'female', true),
('GEN003', 'GENDER', 'other', 'Other', 'Khác', 3, '#6B7280', 'user', true),

-- Blood Types
('BLOOD001', 'BLOOD_TYPE', 'A+', 'A Positive', 'A Dương', 1, '#DC2626', 'droplet', true),
('BLOOD002', 'BLOOD_TYPE', 'A-', 'A Negative', 'A Âm', 2, '#DC2626', 'droplet', true),
('BLOOD003', 'BLOOD_TYPE', 'B+', 'B Positive', 'B Dương', 3, '#3B82F6', 'droplet', true),
('BLOOD004', 'BLOOD_TYPE', 'B-', 'B Negative', 'B Âm', 4, '#3B82F6', 'droplet', true),
('BLOOD005', 'BLOOD_TYPE', 'AB+', 'AB Positive', 'AB Dương', 5, '#7C3AED', 'droplet', true),
('BLOOD006', 'BLOOD_TYPE', 'AB-', 'AB Negative', 'AB Âm', 6, '#7C3AED', 'droplet', true),
('BLOOD007', 'BLOOD_TYPE', 'O+', 'O Positive', 'O Dương', 7, '#059669', 'droplet', true),
('BLOOD008', 'BLOOD_TYPE', 'O-', 'O Negative', 'O Âm', 8, '#059669', 'droplet', true),

-- Appointment Types
('APPT001', 'APPOINTMENT_TYPE', 'consultation', 'Consultation', 'Tư vấn', 1, '#3B82F6', 'stethoscope', true),
('APPT002', 'APPOINTMENT_TYPE', 'follow_up', 'Follow-up', 'Tái khám', 2, '#10B981', 'repeat', true),
('APPT003', 'APPOINTMENT_TYPE', 'emergency', 'Emergency', 'Cấp cứu', 3, '#DC2626', 'ambulance', true),
('APPT004', 'APPOINTMENT_TYPE', 'surgery', 'Surgery', 'Phẫu thuật', 4, '#EF4444', 'scissors', true),
('APPT005', 'APPOINTMENT_TYPE', 'checkup', 'Health Checkup', 'Khám sức khỏe', 5, '#F59E0B', 'heart', true)
ON CONFLICT (enum_id) DO NOTHING;

-- =====================================================
-- FIX UNIQUE FUNCTION NAME ERROR
-- =====================================================
-- This completely removes old functions and creates new ones with unique names

-- 1. Drop ALL existing validate_enum_value functions completely
DROP FUNCTION IF EXISTS validate_enum_value CASCADE;
DROP FUNCTION IF EXISTS validate_enum_value_nullable CASCADE;
DROP FUNCTION IF EXISTS validate_enum_nullable CASCADE;

-- Also drop any variations that might exist
DROP FUNCTION IF EXISTS validate_enum_value(text, text) CASCADE;
DROP FUNCTION IF EXISTS validate_enum_value(anyelement, text) CASCADE;
DROP FUNCTION IF EXISTS validate_enum_value(unknown, text) CASCADE;

-- 2. Create a completely new function with a unique name
CREATE OR REPLACE FUNCTION hospital_validate_enum(value_input anyelement, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  text_value text;
BEGIN
  -- Convert any input type to text safely
  BEGIN
    -- Handle NULL case first
    IF value_input IS NULL THEN
      RETURN true;
    END IF;
    
    -- Convert to text
    text_value := value_input::text;
    
    -- Handle empty string
    IF text_value = '' THEN
      RETURN true;
    END IF;
    
  EXCEPTION
    WHEN OTHERS THEN
      -- If conversion fails, return true (permissive)
      RETURN true;
  END;

  -- Validate based on enum type
  CASE enum_type
    WHEN 'user_role' THEN
      RETURN text_value IN ('patient', 'doctor', 'admin');
    
    WHEN 'gender' THEN
      RETURN text_value IN ('male', 'female', 'other');
    
    WHEN 'blood_type' THEN
      RETURN text_value IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-');
    
    WHEN 'appointment_status' THEN
      RETURN text_value IN ('scheduled', 'confirmed', 'in_progress', 'completed', 'cancelled', 'no_show');
    
    WHEN 'qualification' THEN
      RETURN text_value IN ('Bác sĩ', 'Thạc sĩ', 'Tiến sĩ', 'Giáo sư', 'Phó Giáo sư');
    
    WHEN 'specialization' THEN
      RETURN text_value IN (
        'Nội tổng hợp', 'Ngoại tổng hợp', 'Sản phụ khoa', 'Nhi khoa',
        'Tim mạch can thiệp', 'Thần kinh học', 'Chấn thương và chỉnh hình',
        'Cấp cứu và hồi sức', 'Da liễu', 'Mắt', 'Tai mũi họng', 'Răng hàm mặt'
      );
    
    WHEN 'user_status' THEN
      RETURN text_value IN ('active', 'inactive', 'pending', 'suspended');
    
    ELSE
      -- For unknown enum types, return true (permissive)
      RETURN true;
  END CASE;
  
EXCEPTION
  WHEN OTHERS THEN
    -- If anything fails, return true (permissive)
    RETURN true;
END;
$$;

-- 3. Create the original function name as an alias to the new function
-- This ensures backward compatibility
CREATE OR REPLACE FUNCTION validate_enum_value(value_input anyelement, enum_type text)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Simply call the new function
  RETURN hospital_validate_enum(value_input, enum_type);
END;
$$;

-- 4. Grant permissions to both functions
GRANT EXECUTE ON FUNCTION hospital_validate_enum(anyelement, text) TO authenticated, anon, service_role, postgres;
GRANT EXECUTE ON FUNCTION validate_enum_value(anyelement, text) TO authenticated, anon, service_role, postgres;

-- 5. Remove any problematic constraints that reference the old function
DO $$
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT tc.table_name, tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
        WHERE tc.constraint_type = 'CHECK' 
        AND cc.check_clause LIKE '%validate_enum_value%'
    LOOP
        EXECUTE format('ALTER TABLE %I DROP CONSTRAINT IF EXISTS %I', 
                      constraint_record.table_name, 
                      constraint_record.constraint_name);
        RAISE NOTICE 'Dropped constraint % from table %', 
                     constraint_record.constraint_name, 
                     constraint_record.table_name;
    END LOOP;
END $$;

-- 6. Test both functions
SELECT 
  'Testing new hospital_validate_enum function...' as test_start;

SELECT 
  'patient test' as test_name,
  hospital_validate_enum('patient', 'user_role') as result;

SELECT 
  'timestamp test' as test_name,
  hospital_validate_enum(NOW(), 'user_role') as result;

SELECT 
  'null test' as test_name,
  hospital_validate_enum(NULL::text, 'user_role') as result;

SELECT 
  'Testing backward compatible validate_enum_value...' as test_start;

SELECT 
  'patient test via alias' as test_name,
  validate_enum_value('patient', 'user_role') as result;

-- 7. Verify functions exist
SELECT 
  proname as function_name,
  pg_get_function_arguments(oid) as arguments
FROM pg_proc 
WHERE proname IN ('hospital_validate_enum', 'validate_enum_value')
ORDER BY proname;

-- 8. Success message
SELECT 'Functions created with unique names - no more conflicts!' as status;

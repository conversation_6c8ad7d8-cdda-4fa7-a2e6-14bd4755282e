-- =====================================================
-- REMOVE DYNAMIC ENUM SYSTEM
-- =====================================================
-- Completely removes all dynamic enum tables, functions, views, and constraints

-- 1. Drop all dynamic enum constraints from existing tables
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_dynamic_specialization;
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_dynamic_status;
ALTER TABLE patients DROP CONSTRAINT IF EXISTS check_dynamic_patient_status;
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS check_dynamic_appointment_status;
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS check_dynamic_appointment_type;
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS check_dynamic_room_type;
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS check_dynamic_room_status;
ALTER TABLE departments DROP CONSTRAINT IF EXISTS check_dynamic_department_type;
ALTER TABLE medical_records DROP CONSTRAINT IF EXISTS check_dynamic_medical_record_status;
ALTER TABLE prescriptions DROP CONSTRAINT IF EXISTS check_dynamic_prescription_status;
ALTER TABLE prescriptions DROP CONSTRAINT IF EXISTS check_dynamic_medication_type;
ALTER TABLE billing DROP CONSTRAINT IF EXISTS check_dynamic_payment_method;

-- 2. Drop all dynamic enum functions
DROP FUNCTION IF EXISTS validate_dynamic_enum_value(text, text);
DROP FUNCTION IF EXISTS validate_dynamic_enum_value_nullable(text, text);
DROP FUNCTION IF EXISTS get_enum_display_name(text, text, text);
DROP FUNCTION IF EXISTS get_enum_options(text, text);

-- 3. Drop all dynamic enum views
DROP VIEW IF EXISTS v_enum_categories;
DROP VIEW IF EXISTS v_system_enums;
DROP VIEW IF EXISTS v_active_enums;

-- 4. Drop triggers
DROP TRIGGER IF EXISTS update_enum_categories_updated_at ON enum_categories;
DROP TRIGGER IF EXISTS update_system_enums_updated_at ON system_enums;

-- 5. Drop the main dynamic enum tables
DROP TABLE IF EXISTS system_enums CASCADE;
DROP TABLE IF EXISTS enum_categories CASCADE;

-- 6. DO NOT drop update_updated_at_column() function as it's used by other tables

-- Success message
SELECT 'Dynamic Enum System completely removed!' as status;

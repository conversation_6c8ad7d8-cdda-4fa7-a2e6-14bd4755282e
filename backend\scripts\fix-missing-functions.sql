-- =====================================================
-- FIX MISSING DYNAMIC ENUM FUNCTIONS
-- =====================================================
-- Creates missing validation functions for dynamic enum system

-- 1. Create dynamic validation function
CREATE OR REPLACE FUNCTION validate_dynamic_enum_value(
  value_to_check text, 
  category_id text
)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Handle NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;

  -- Check if value exists in system_enums table
  RETURN EXISTS (
    SELECT 1 
    FROM system_enums 
    WHERE category_id = validate_dynamic_enum_value.category_id
      AND enum_key = value_to_check
      AND is_active = true
  );
END;
$$;

-- 2. Create nullable version
CREATE OR REPLACE FUNCTION validate_dynamic_enum_value_nullable(
  value_to_check text, 
  category_id text
)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Always allow NULL
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Use main validation for non-null values
  RETURN validate_dynamic_enum_value(value_to_check, category_id);
END;
$$;

-- 3. Create helper function to get enum display name
CREATE OR REPLACE FUNCTION get_enum_display_name(
  p_category_id text,
  p_enum_key text,
  p_language text DEFAULT 'vi'
)
RETURNS text
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  result text;
BEGIN
  IF p_language = 'en' THEN
    SELECT display_name_en INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  ELSE
    SELECT display_name_vi INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  END IF;
  
  RETURN COALESCE(result, p_enum_key);
END;
$$;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION validate_dynamic_enum_value(text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_dynamic_enum_value_nullable(text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION get_enum_display_name(text, text, text) TO authenticated, anon, service_role;

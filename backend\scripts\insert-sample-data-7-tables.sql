-- =====================================================
-- INSERT SAMPLE DATA FOR 7 SEPARATE TABLES
-- =====================================================
-- Run this after creating the 7 tables

INSERT INTO departments_enum (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order) VALUES
('emergency', 'Emergency Department', 'Khoa Cấp cứu', 'Emergency and trauma care', 'Cấp cứu và chấn thương', '#DC2626', 'ambulance', 1),
('internal_medicine', 'Internal Medicine', 'Khoa Nội', 'Internal medicine department', 'Khoa nội tổng hợp', '#3B82F6', 'stethoscope', 2),
('surgery', 'Surgery Department', '<PERSON><PERSON><PERSON>', 'Surgical department', 'Khoa phẫu thuật', '#EF4444', 'scissors', 3),
('pediatrics', 'Pediatrics', 'Khoa Nhi', 'Children department', 'Khoa trẻ em', '#10B981', 'baby', 4),
('cardiology', 'Cardiology', 'Khoa Tim mạch', 'Heart department', 'Khoa tim mạch', '#DC2626', 'heart', 5),
('radiology', 'Radiology', 'Khoa Chẩn đoán hình ảnh', 'Medical imaging', 'Chẩn đoán hình ảnh y khoa', '#6366F1', 'scan', 6)
ON CONFLICT (code) DO NOTHING;

INSERT INTO room_types (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order) VALUES
('consultation', 'Consultation Room', 'Phòng khám', 'Doctor consultation room', 'Phòng khám bác sĩ', '#3B82F6', 'stethoscope', 1),
('surgery', 'Surgery Room', 'Phòng mổ', 'Operating theater', 'Phòng phẫu thuật', '#EF4444', 'scissors', 2),
('emergency', 'Emergency Room', 'Phòng cấp cứu', 'Emergency treatment room', 'Phòng điều trị cấp cứu', '#DC2626', 'ambulance', 3),
('ward', 'Ward Room', 'Phòng bệnh', 'Patient ward room', 'Phòng nằm bệnh nhân', '#10B981', 'bed', 4),
('icu', 'ICU', 'Phòng hồi sức', 'Intensive care unit', 'Đơn vị chăm sóc đặc biệt', '#7C2D12', 'heart-pulse', 5),
('laboratory', 'Laboratory', 'Phòng xét nghiệm', 'Medical laboratory', 'Phòng xét nghiệm y khoa', '#8B5CF6', 'flask', 6)
ON CONFLICT (code) DO NOTHING;

INSERT INTO diagnosis (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order) VALUES
('hypertension', 'Hypertension', 'Tăng huyết áp', 'High blood pressure', 'Huyết áp cao', '#DC2626', 'heart', 1),
('diabetes', 'Diabetes Mellitus', 'Đái tháo đường', 'Diabetes type 1 and 2', 'Bệnh đái tháo đường', '#F59E0B', 'droplet', 2),
('pneumonia', 'Pneumonia', 'Viêm phổi', 'Lung infection', 'Nhiễm trùng phổi', '#3B82F6', 'lungs', 3),
('gastritis', 'Gastritis', 'Viêm dạ dày', 'Stomach inflammation', 'Viêm niêm mạc dạ dày', '#10B981', 'stomach', 4),
('migraine', 'Migraine', 'Đau nửa đầu', 'Severe headache', 'Đau đầu dữ dội', '#7C3AED', 'brain', 5)
ON CONFLICT (code) DO NOTHING;

INSERT INTO medications (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order) VALUES
('antibiotics', 'Antibiotics', 'Kháng sinh', 'Bacterial infection treatment', 'Điều trị nhiễm trùng vi khuẩn', '#EF4444', 'pill', 1),
('analgesics', 'Analgesics', 'Thuốc giảm đau', 'Pain relief medication', 'Thuốc giảm đau', '#F59E0B', 'zap-off', 2),
('vitamins', 'Vitamins', 'Vitamin', 'Nutritional supplements', 'Thực phẩm bổ sung dinh dưỡng', '#059669', 'apple', 3),
('vaccines', 'Vaccines', 'Vaccine', 'Immunization', 'Tiêm chủng', '#3B82F6', 'syringe', 4),
('emergency', 'Emergency Medications', 'Thuốc cấp cứu', 'Emergency treatment drugs', 'Thuốc điều trị cấp cứu', '#DC2626', 'ambulance', 5)
ON CONFLICT (code) DO NOTHING;

INSERT INTO status_values (code, name_en, name_vi, description_en, description_vi, applies_to, color_code, icon_name, sort_order) VALUES
('active', 'Active', 'Hoạt động', 'Currently active', 'Đang hoạt động', 'general', '#10B981', 'check-circle', 1),
('inactive', 'Inactive', 'Không hoạt động', 'Currently inactive', 'Không hoạt động', 'general', '#6B7280', 'x-circle', 2),
('pending', 'Pending', 'Chờ xử lý', 'Waiting for action', 'Đang chờ xử lý', 'general', '#F59E0B', 'clock', 3),
('completed', 'Completed', 'Hoàn thành', 'Successfully completed', 'Đã hoàn thành', 'general', '#059669', 'check-circle-2', 4),
('cancelled', 'Cancelled', 'Đã hủy', 'Cancelled or terminated', 'Đã bị hủy bỏ', 'general', '#EF4444', 'x-circle', 5)
ON CONFLICT (code) DO NOTHING;

INSERT INTO payment_methods (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order) VALUES
('cash', 'Cash', 'Tiền mặt', 'Cash payment', 'Thanh toán bằng tiền mặt', '#10B981', 'banknote', 1),
('credit_card', 'Credit Card', 'Thẻ tín dụng', 'Credit card payment', 'Thanh toán bằng thẻ tín dụng', '#3B82F6', 'credit-card', 2),
('bank_transfer', 'Bank Transfer', 'Chuyển khoản', 'Electronic bank transfer', 'Chuyển khoản ngân hàng', '#6366F1', 'building-2', 3),
('insurance', 'Insurance', 'Bảo hiểm', 'Insurance coverage', 'Thanh toán qua bảo hiểm', '#7C3AED', 'shield-check', 4),
('mobile_payment', 'Mobile Payment', 'Thanh toán di động', 'Mobile app payment', 'Thanh toán qua ứng dụng di động', '#F59E0B', 'smartphone', 5)
ON CONFLICT (code) DO NOTHING;

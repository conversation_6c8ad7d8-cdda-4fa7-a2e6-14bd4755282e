-- =====================================================
-- FIXED ENUM REMOVAL - SAFE VERSION
-- =====================================================
-- This version safely removes dynamic enum system without breaking other tables

-- STEP 1: Create 7 separate tables first
CREATE TABLE IF NOT EXISTS specialties (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS departments_enum (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS room_types (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS diagnosis (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  icd_code VARCHAR(20),
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS medications (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  drug_class VARCHAR(100),
  dosage_form VARCHAR(50),
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS status_values (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  applies_to VARCHAR(100),
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS payment_methods (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  requires_verification BOOLEAN DEFAULT false,
  processing_fee DECIMAL(5,2) DEFAULT 0.00,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- STEP 3: Remove dynamic enum system SAFELY
-- Drop constraints first
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_dynamic_specialization;
ALTER TABLE doctors DROP CONSTRAINT IF EXISTS check_dynamic_status;
ALTER TABLE patients DROP CONSTRAINT IF EXISTS check_dynamic_patient_status;
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS check_dynamic_appointment_status;
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS check_dynamic_appointment_type;
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS check_dynamic_room_type;
ALTER TABLE rooms DROP CONSTRAINT IF EXISTS check_dynamic_room_status;
ALTER TABLE departments DROP CONSTRAINT IF EXISTS check_dynamic_department_type;
ALTER TABLE medical_records DROP CONSTRAINT IF EXISTS check_dynamic_medical_record_status;
ALTER TABLE prescriptions DROP CONSTRAINT IF EXISTS check_dynamic_prescription_status;
ALTER TABLE prescriptions DROP CONSTRAINT IF EXISTS check_dynamic_medication_type;
ALTER TABLE billing DROP CONSTRAINT IF EXISTS check_dynamic_payment_method;

-- Drop dynamic enum functions
DROP FUNCTION IF EXISTS validate_dynamic_enum_value(text, text);
DROP FUNCTION IF EXISTS validate_dynamic_enum_value_nullable(text, text);
DROP FUNCTION IF EXISTS get_enum_display_name(text, text, text);
DROP FUNCTION IF EXISTS get_enum_options(text, text);

-- Drop views
DROP VIEW IF EXISTS v_enum_categories;
DROP VIEW IF EXISTS v_system_enums;
DROP VIEW IF EXISTS v_active_enums;

-- Drop triggers (only for enum tables)
DROP TRIGGER IF EXISTS update_enum_categories_updated_at ON enum_categories;
DROP TRIGGER IF EXISTS update_system_enums_updated_at ON system_enums;

-- Drop the main dynamic enum tables
DROP TABLE IF EXISTS system_enums CASCADE;
DROP TABLE IF EXISTS enum_categories CASCADE;

-- Success message
SELECT 'Fixed Enum Removal completed successfully!' as status;
-- =====================================================
-- DYNAMIC ENUM SYSTEM - DATABASE SCHEMA
-- =====================================================
-- Creates tables and functions for dynamic enum management

-- 1. Create enum_categories table
CREATE TABLE IF NOT EXISTS enum_categories (
  category_id VARCHAR(50) PRIMARY KEY,
  category_name VARCHAR(100) NOT NULL UNIQUE,
  display_name_en VARCHAR(200) NOT NULL,
  display_name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. Create system_enums table
CREATE TABLE IF NOT EXISTS system_enums (
  enum_id VARCHAR(50) PRIMARY KEY,
  category_id VARCHAR(50) NOT NULL,
  enum_key VARCHAR(100) NOT NULL,
  display_name_en VARCHAR(200) NOT NULL,
  display_name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  sort_order INTEGER DEFAULT 0,
  color_code VARCHAR(7), -- Hex color code
  icon_name VARCHAR(50),
  is_default BOOLEAN DEFAULT false,
  is_system BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  metadata JSONB,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (category_id) REFERENCES enum_categories(category_id) ON DELETE CASCADE,
  UNIQUE(category_id, enum_key)
);

-- 3. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_enum_categories_active ON enum_categories(is_active);
CREATE INDEX IF NOT EXISTS idx_enum_categories_system ON enum_categories(is_system);
CREATE INDEX IF NOT EXISTS idx_system_enums_category ON system_enums(category_id);
CREATE INDEX IF NOT EXISTS idx_system_enums_active ON system_enums(is_active);
CREATE INDEX IF NOT EXISTS idx_system_enums_category_key ON system_enums(category_id, enum_key) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_system_enums_sort ON system_enums(category_id, sort_order);

-- 4. Create view for enum categories with counts
CREATE OR REPLACE VIEW v_enum_categories AS
SELECT 
  ec.*,
  COUNT(se.enum_id) as enum_count
FROM enum_categories ec
LEFT JOIN system_enums se ON ec.category_id = se.category_id AND se.is_active = true
WHERE ec.is_active = true
GROUP BY ec.category_id, ec.category_name, ec.display_name_en, ec.display_name_vi, 
         ec.description_en, ec.description_vi, ec.is_system, ec.is_active, 
         ec.created_at, ec.updated_at
ORDER BY ec.category_name;

-- 5. Create view for system enums with category info
CREATE OR REPLACE VIEW v_system_enums AS
SELECT 
  se.*,
  ec.category_name,
  ec.display_name_en as category_display_en,
  ec.display_name_vi as category_display_vi
FROM system_enums se
JOIN enum_categories ec ON se.category_id = ec.category_id
WHERE se.is_active = true AND ec.is_active = true
ORDER BY ec.category_name, se.sort_order, se.display_name_vi;

-- 6. Create dynamic validation function
CREATE OR REPLACE FUNCTION validate_dynamic_enum_value(
  value_to_check text, 
  category_id text
)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Handle NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;

  -- Check if value exists in system_enums table
  RETURN EXISTS (
    SELECT 1 
    FROM system_enums 
    WHERE category_id = validate_dynamic_enum_value.category_id
      AND enum_key = value_to_check
      AND is_active = true
  );
END;
$$;

-- 7. Create nullable version
CREATE OR REPLACE FUNCTION validate_dynamic_enum_value_nullable(
  value_to_check text, 
  category_id text
)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Always allow NULL
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Use main validation for non-null values
  RETURN validate_dynamic_enum_value(value_to_check, category_id);
END;
$$;

-- 8. Create helper function to get enum display name
CREATE OR REPLACE FUNCTION get_enum_display_name(
  p_category_id text,
  p_enum_key text,
  p_language text DEFAULT 'vi'
)
RETURNS text
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  result text;
BEGIN
  IF p_language = 'en' THEN
    SELECT display_name_en INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  ELSE
    SELECT display_name_vi INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  END IF;
  
  RETURN COALESCE(result, p_enum_key);
END;
$$;

-- 9. Create function to get all enum options for a category
CREATE OR REPLACE FUNCTION get_enum_options(
  p_category_id text,
  p_language text DEFAULT 'vi'
)
RETURNS TABLE(
  enum_key text,
  display_name text,
  description text,
  sort_order integer,
  color_code text,
  icon_name text,
  is_default boolean
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  IF p_language = 'en' THEN
    RETURN QUERY
    SELECT 
      se.enum_key,
      se.display_name_en as display_name,
      se.description_en as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id 
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_en;
  ELSE
    RETURN QUERY
    SELECT 
      se.enum_key,
      se.display_name_vi as display_name,
      se.description_vi as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id 
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_vi;
  END IF;
END;
$$;

-- 10. Grant permissions
GRANT ALL ON enum_categories TO authenticated, anon, service_role;
GRANT ALL ON system_enums TO authenticated, anon, service_role;
GRANT SELECT ON v_enum_categories TO authenticated, anon, service_role;
GRANT SELECT ON v_system_enums TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_dynamic_enum_value(text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_dynamic_enum_value_nullable(text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION get_enum_display_name(text, text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION get_enum_options(text, text) TO authenticated, anon, service_role;

-- 11. Create trigger to update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_enum_categories_updated_at 
    BEFORE UPDATE ON enum_categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_system_enums_updated_at 
    BEFORE UPDATE ON system_enums 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

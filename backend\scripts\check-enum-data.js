// =====================================================
// CHECK DYNAMIC ENUM DATA
// =====================================================
// Script to check if dynamic enum data is properly set up

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ciasxktujslgsdgylimv.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('❌ Missing Supabase service role key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function checkEnumData() {
  console.log('🔍 Checking Dynamic Enum Data on Supabase...\n');

  try {
    // 1. Check if tables exist
    console.log('📋 Step 1: Checking table structure...');
    
    const { data: tables, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .in('table_name', ['enum_categories', 'system_enums']);

    if (tableError) {
      console.log('⚠️  Cannot check table structure via query, trying direct access...');
    } else {
      console.log(`✅ Found ${tables?.length || 0} enum tables`);
    }

    // 2. Check enum_categories
    console.log('\n📋 Step 2: Checking enum_categories...');
    const { data: categories, error: catError } = await supabase
      .from('enum_categories')
      .select('*')
      .order('category_id');

    if (catError) {
      console.error('❌ Error accessing enum_categories:', catError.message);
      return;
    }

    console.log(`✅ Found ${categories.length} categories:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.category_id}: ${cat.display_name_vi} (${cat.display_name_en})`);
    });

    // 3. Check system_enums
    console.log('\n📋 Step 3: Checking system_enums...');
    const { data: enums, error: enumError } = await supabase
      .from('system_enums')
      .select('*')
      .order('category_id, sort_order');

    if (enumError) {
      console.error('❌ Error accessing system_enums:', enumError.message);
      return;
    }

    console.log(`✅ Found ${enums.length} enum values:`);
    
    // Group by category
    const enumsByCategory = {};
    enums.forEach(enumItem => {
      if (!enumsByCategory[enumItem.category_id]) {
        enumsByCategory[enumItem.category_id] = [];
      }
      enumsByCategory[enumItem.category_id].push(enumItem);
    });

    Object.keys(enumsByCategory).forEach(categoryId => {
      const categoryEnums = enumsByCategory[categoryId];
      console.log(`\n   📁 ${categoryId} (${categoryEnums.length} items):`);
      categoryEnums.forEach(enumItem => {
        const color = enumItem.color_code ? `[${enumItem.color_code}]` : '';
        const icon = enumItem.icon_name ? `{${enumItem.icon_name}}` : '';
        console.log(`      - ${enumItem.enum_key}: ${enumItem.display_name_vi} ${color} ${icon}`);
      });
    });

    // 4. Check views
    console.log('\n📋 Step 4: Checking views...');
    
    try {
      const { data: viewCategories, error: viewCatError } = await supabase
        .from('v_enum_categories')
        .select('category_id, category_name, enum_count')
        .limit(10);

      if (viewCatError) {
        console.log('⚠️  v_enum_categories view not accessible:', viewCatError.message);
      } else {
        console.log('✅ v_enum_categories view working:');
        viewCategories.forEach(cat => {
          console.log(`   - ${cat.category_id}: ${cat.enum_count} enums`);
        });
      }
    } catch (err) {
      console.log('⚠️  Views may not be created yet');
    }

    // 5. Check functions
    console.log('\n📋 Step 5: Testing functions...');
    
    try {
      const { data: funcResult, error: funcError } = await supabase
        .rpc('validate_dynamic_enum_value', {
          value_to_check: 'active',
          category_id: 'STATUS_VALUES'
        });

      if (funcError) {
        console.log('⚠️  validate_dynamic_enum_value function not working:', funcError.message);
      } else {
        console.log(`✅ validate_dynamic_enum_value function working: ${funcResult}`);
      }
    } catch (err) {
      console.log('⚠️  Functions may not be created yet');
    }

    // 6. Data completeness check
    console.log('\n📋 Step 6: Data completeness check...');
    
    const expectedCategories = [
      'SPECIALTIES', 'DEPARTMENTS', 'ROOM_TYPES', 
      'DIAGNOSIS', 'MEDICATIONS', 'STATUS_VALUES', 'PAYMENT_METHODS'
    ];

    const foundCategories = categories.map(cat => cat.category_id);
    const missingCategories = expectedCategories.filter(cat => !foundCategories.includes(cat));
    
    if (missingCategories.length === 0) {
      console.log('✅ All expected categories found');
    } else {
      console.log(`⚠️  Missing categories: ${missingCategories.join(', ')}`);
    }

    // Check if each category has enums
    const categoriesWithoutEnums = [];
    expectedCategories.forEach(categoryId => {
      const categoryEnums = enumsByCategory[categoryId];
      if (!categoryEnums || categoryEnums.length === 0) {
        categoriesWithoutEnums.push(categoryId);
      }
    });

    if (categoriesWithoutEnums.length === 0) {
      console.log('✅ All categories have enum values');
    } else {
      console.log(`⚠️  Categories without enums: ${categoriesWithoutEnums.join(', ')}`);
    }

    // 7. Summary
    console.log('\n📊 Summary:');
    console.log('='.repeat(50));
    console.log(`📁 Categories: ${categories.length}`);
    console.log(`📝 Enum values: ${enums.length}`);
    console.log(`🎯 Expected categories: ${expectedCategories.length}`);
    console.log(`✅ Complete categories: ${expectedCategories.length - missingCategories.length - categoriesWithoutEnums.length}`);
    
    if (missingCategories.length === 0 && categoriesWithoutEnums.length === 0) {
      console.log('\n🎉 Dynamic Enum System is fully set up and ready to use!');
      
      console.log('\n📚 Usage examples:');
      console.log('Frontend Hook:');
      console.log('```tsx');
      console.log('import { useSpecialties } from "@/lib/hooks/useDynamicEnums";');
      console.log('const { options, loading } = useSpecialties("vi");');
      console.log('```');
      
      console.log('\nDirect Service:');
      console.log('```typescript');
      console.log('import { enumService } from "@/lib/services/enum.service";');
      console.log('const specialties = await enumService.getSpecialties("vi");');
      console.log('```');
      
    } else {
      console.log('\n⚠️  System needs additional setup. Missing data detected.');
      
      if (missingCategories.length > 0) {
        console.log('\n🔧 To fix missing categories, run this SQL in Supabase:');
        missingCategories.forEach(categoryId => {
          console.log(`INSERT INTO enum_categories (category_id, category_name, display_name_en, display_name_vi, is_system) VALUES ('${categoryId}', '${categoryId}', '${categoryId}', '${categoryId}', true);`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Error checking enum data:', error);
  }
}

// Run the check
if (require.main === module) {
  checkEnumData()
    .catch(error => {
      console.error('❌ Check failed:', error);
      process.exit(1);
    });
}

module.exports = { checkEnumData };

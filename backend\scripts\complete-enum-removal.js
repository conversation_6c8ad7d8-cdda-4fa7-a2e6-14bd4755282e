#!/usr/bin/env node

/**
 * Complete Dynamic Enum System Removal
 * Loại bỏ hoàn toàn hệ thống enum động và thay thế bằng 7 bảng riêng biệt
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'your-service-role-key';

const supabase = createClient(supabaseUrl, supabaseKey);

async function executeSQL(filename) {
  const filePath = path.join(__dirname, filename);
  const sql = fs.readFileSync(filePath, 'utf8');
  
  console.log(`📄 Executing ${filename}...`);
  
  // Split SQL into individual statements
  const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
  
  for (let i = 0; i < statements.length; i++) {
    const statement = statements[i].trim();
    if (statement.length === 0) continue;
    
    try {
      const { data, error } = await supabase.rpc('exec_sql', { 
        sql_query: statement + ';' 
      });
      
      if (error) {
        console.log(`   ⚠️  Statement ${i + 1}: ${error.message}`);
      } else {
        console.log(`   ✅ Statement ${i + 1}: Success`);
      }
    } catch (err) {
      console.log(`   ⚠️  Statement ${i + 1}: ${err.message}`);
    }
  }
  
  console.log(`✅ ${filename} executed successfully`);
  return true;
}

async function completeEnumRemoval() {
  console.log('🗑️  Starting Complete Dynamic Enum System Removal...\n');

  try {
    // Step 1: Create 7 separate tables first
    console.log('📋 Step 1: Creating 7 separate enum tables...');
    await executeSQL('create-7-separate-tables.sql');
    console.log('✅ 7 separate tables created successfully\n');

    // Step 2: Migrate data from dynamic enum to 7 tables
    console.log('📋 Step 2: Migrating data to 7 separate tables...');
    await executeSQL('migrate-data-to-7-tables.sql');
    console.log('✅ Data migration completed successfully\n');

    // Step 3: Remove dynamic enum system
    console.log('📋 Step 3: Removing dynamic enum system...');
    await executeSQL('remove-dynamic-enum-system.sql');
    console.log('✅ Dynamic enum system removed successfully\n');

    // Step 4: Verify the new system
    console.log('📋 Step 4: Verifying new system...');
    
    const tables = ['specialties', 'departments_enum', 'room_types', 'diagnosis', 'medications', 'status_values', 'payment_methods'];
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count(*)', { count: 'exact' });
        
        if (error) {
          console.log(`   ⚠️  ${table}: Error - ${error.message}`);
        } else {
          console.log(`   ✅ ${table}: ${data.length} records`);
        }
      } catch (err) {
        console.log(`   ⚠️  ${table}: ${err.message}`);
      }
    }

    console.log('\n🎉 Dynamic Enum System Removal Completed Successfully!');
    console.log('\n📊 Summary:');
    console.log('✅ 7 separate enum tables created');
    console.log('✅ Data migrated from dynamic enum system');
    console.log('✅ Dynamic enum system completely removed');
    console.log('✅ New system verified and working');
    
    console.log('\n🔧 Next steps:');
    console.log('1. Update frontend code to use new table structure');
    console.log('2. Remove dynamic enum components and services');
    console.log('3. Update API endpoints to use new tables');
    console.log('4. Test all forms and dropdowns');

  } catch (error) {
    console.error('\n❌ Removal failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check Supabase connection');
    console.log('2. Verify environment variables');
    console.log('3. Check database permissions');
    console.log('4. Review SQL scripts for errors');
    process.exit(1);
  }
}

// Execute if run directly
if (require.main === module) {
  completeEnumRemoval();
}

module.exports = { completeEnumRemoval };

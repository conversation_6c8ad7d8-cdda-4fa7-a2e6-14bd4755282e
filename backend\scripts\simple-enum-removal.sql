-- =====================================================
-- SIMPLE ENUM REMOVAL - RUN IN SUPABASE SQL EDITOR
-- =====================================================
-- Copy and paste this entire script into Supabase SQL Editor

-- STEP 1: Create 7 separate tables
CREATE TABLE IF NOT EXISTS specialties (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS departments_enum (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS room_types (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS diagnosis (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  icd_code VARCHAR(20),
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS medications (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  drug_class VARCHAR(100),
  dosage_form VARCHAR(50),
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS status_values (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  applies_to VARCHAR(100),
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS payment_methods (
  id SERIAL PRIMARY KEY,
  code VARCHAR(50) UNIQUE NOT NULL,
  name_en VARCHAR(200) NOT NULL,
  name_vi VARCHAR(200) NOT NULL,
  description_en TEXT,
  description_vi TEXT,
  requires_verification BOOLEAN DEFAULT false,
  processing_fee DECIMAL(5,2) DEFAULT 0.00,
  color_code VARCHAR(7),
  icon_name VARCHAR(50),
  sort_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- STEP 2: Insert sample data into 7 tables
INSERT INTO specialties (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order) VALUES
('internal_medicine', 'Internal Medicine', 'Nội tổng hợp', 'General internal medicine', 'Nội khoa tổng hợp', '#3B82F6', 'heart', 1),
('surgery', 'Surgery', 'Ngoại tổng hợp', 'General surgery', 'Ngoại khoa tổng hợp', '#EF4444', 'scissors', 2),
('pediatrics', 'Pediatrics', 'Nhi khoa', 'Children medicine', 'Y học trẻ em', '#10B981', 'baby', 3),
('cardiology', 'Cardiology', 'Tim mạch', 'Heart and cardiovascular', 'Tim mạch và hệ tuần hoàn', '#DC2626', 'heart-pulse', 4),
('neurology', 'Neurology', 'Thần kinh', 'Nervous system', 'Hệ thần kinh', '#7C3AED', 'brain', 5),
('orthopedics', 'Orthopedics', 'Chấn thương chỉnh hình', 'Bone and joint surgery', 'Phẫu thuật xương khớp', '#059669', 'bone', 6),
('emergency', 'Emergency Medicine', 'Cấp cứu', 'Emergency and critical care', 'Cấp cứu và hồi sức', '#DC2626', 'ambulance', 7),
('dermatology', 'Dermatology', 'Da liễu', 'Skin conditions', 'Bệnh lý da', '#F97316', 'skin', 8)
ON CONFLICT (code) DO NOTHING;
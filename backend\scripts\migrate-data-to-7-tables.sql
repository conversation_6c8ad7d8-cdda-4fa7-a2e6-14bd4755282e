-- =====================================================
-- MIGRATE DATA FROM DYNAMIC ENUM TO 7 SEPARATE TABLES
-- =====================================================
-- Migrates existing data from system_enums to individual tables

-- 1. Migrate SPECIALTIES data
INSERT INTO specialties (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'SPECIALTIES' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- 2. Migrate DEPARTMENTS data
INSERT INTO departments_enum (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'DEPARTMENTS' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- 3. Migrate ROOM_TYPES data
INSERT INTO room_types (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'ROOM_TYPES' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- 4. Migrate DIAGNOSIS data
INSERT INTO diagnosis (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'DIAGNOSIS' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- 5. Migrate MEDICATIONS data
INSERT INTO medications (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'MEDICATIONS' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- 6. Migrate STATUS_VALUES data
INSERT INTO status_values (code, name_en, name_vi, description_en, description_vi, applies_to, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  'general' as applies_to, -- Default value
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'STATUS_VALUES' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- 7. Migrate PAYMENT_METHODS data
INSERT INTO payment_methods (code, name_en, name_vi, description_en, description_vi, color_code, icon_name, sort_order, is_active)
SELECT 
  enum_key as code,
  display_name_en as name_en,
  display_name_vi as name_vi,
  description_en,
  description_vi,
  color_code,
  icon_name,
  sort_order,
  is_active
FROM system_enums 
WHERE category_id = 'PAYMENT_METHODS' AND is_active = true
ON CONFLICT (code) DO NOTHING;

-- Success message
SELECT 'Data migration completed successfully!' as status;

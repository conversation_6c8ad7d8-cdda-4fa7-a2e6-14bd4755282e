-- =====================================================
-- DYNAMIC ENUM VALIDATION - IMPROVED VERSION
-- =====================================================
-- This replaces static enum validation with dynamic lookup

-- 1. Drop old static validation functions
DROP FUNCTION IF EXISTS validate_enum_value CASCADE;
DROP FUNCTION IF EXISTS validate_enum_value_nullable CASCADE;

-- 2. Create dynamic enum validation function
CREATE OR REPLACE FUNCTION validate_dynamic_enum_value(
  value_to_check text, 
  category_id text
)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Handle NULL values
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;

  -- Check if value exists in system_enums table
  RETURN EXISTS (
    SELECT 1 
    FROM system_enums 
    WHERE category_id = validate_dynamic_enum_value.category_id
      AND enum_key = value_to_check
      AND is_active = true
  );
END;
$$;

-- 3. Create nullable version
CREATE OR REPLACE FUNCTION validate_dynamic_enum_value_nullable(
  value_to_check text, 
  category_id text
)
RETURNS boolean
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  -- Always allow NULL
  IF value_to_check IS NULL THEN
    RETURN true;
  END IF;
  
  -- Use main validation for non-null values
  RETURN validate_dynamic_enum_value(value_to_check, category_id);
END;
$$;

-- 4. Grant permissions
GRANT EXECUTE ON FUNCTION validate_dynamic_enum_value(text, text) TO authenticated, anon, service_role;
GRANT EXECUTE ON FUNCTION validate_dynamic_enum_value_nullable(text, text) TO authenticated, anon, service_role;

-- 5. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_system_enums_category_key ON system_enums(category_id, enum_key) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_system_enums_active ON system_enums(is_active);

-- 6. Create view for easy enum lookup
CREATE OR REPLACE VIEW v_active_enums AS
SELECT 
  category_id,
  enum_key,
  display_name_vi,
  display_name_en,
  sort_order,
  color_code,
  icon_name,
  is_default
FROM system_enums 
WHERE is_active = true
ORDER BY category_id, sort_order, display_name_vi;

-- 7. Create helper function to get enum display name
CREATE OR REPLACE FUNCTION get_enum_display_name(
  p_category_id text,
  p_enum_key text,
  p_language text DEFAULT 'vi'
)
RETURNS text
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  result text;
BEGIN
  IF p_language = 'en' THEN
    SELECT display_name_en INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  ELSE
    SELECT display_name_vi INTO result
    FROM system_enums
    WHERE category_id = p_category_id 
      AND enum_key = p_enum_key 
      AND is_active = true;
  END IF;
  
  RETURN COALESCE(result, p_enum_key);
END;
$$;

-- 8. Grant permissions for helper function
GRANT EXECUTE ON FUNCTION get_enum_display_name(text, text, text) TO authenticated, anon, service_role;

-- 9. Create function to get all enum options for a category
CREATE OR REPLACE FUNCTION get_enum_options(
  p_category_id text,
  p_language text DEFAULT 'vi'
)
RETURNS TABLE(
  enum_key text,
  display_name text,
  description text,
  sort_order integer,
  color_code text,
  icon_name text,
  is_default boolean
)
LANGUAGE plpgsql
STABLE
AS $$
BEGIN
  IF p_language = 'en' THEN
    RETURN QUERY
    SELECT 
      se.enum_key,
      se.display_name_en as display_name,
      se.description_en as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id 
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_en;
  ELSE
    RETURN QUERY
    SELECT 
      se.enum_key,
      se.display_name_vi as display_name,
      se.description_vi as description,
      se.sort_order,
      se.color_code,
      se.icon_name,
      se.is_default
    FROM system_enums se
    WHERE se.category_id = p_category_id 
      AND se.is_active = true
    ORDER BY se.sort_order, se.display_name_vi;
  END IF;
END;
$$;

-- 10. Grant permissions for options function
GRANT EXECUTE ON FUNCTION get_enum_options(text, text) TO authenticated, anon, service_role;

-- =====================================================
-- DYNAMIC ENUM SYSTEM - SAMPLE DATA
-- =====================================================
-- Inserts sample data for the 7 requested categories

-- 1. Insert enum categories
INSERT INTO enum_categories (category_id, category_name, display_name_en, display_name_vi, description_en, description_vi, is_system) VALUES
('SPECIALTIES', 'Medical Specialties', 'Medical Specialties', 'Chuyên khoa Y tế', 'Medical specialization fields', 'Các lĩnh vực chuyên khoa y tế', true),
('DEPARTMENTS', 'Hospital Departments', 'Hospital Departments', 'Khoa Bệnh viện', 'Hospital departments and units', 'Các khoa và đơn vị bệnh viện', true),
('ROOM_TYPES', 'Room Types', 'Room Types', 'Loại Phòng', 'Types of hospital rooms', 'Các loại phòng bệnh viện', true),
('DIAGNOSIS', 'Medical Diagnosis', 'Medical Diagnosis', 'Chẩn đoán Y khoa', 'Medical diagnosis categories', 'Các danh mục chẩn đoán y khoa', true),
('MEDICATIONS', 'Medications', 'Medications', 'Thuốc', 'Medication categories and types', 'Các loại và danh mục thuốc', true),
('STATUS_VALUES', 'Status Values', 'Status Values', 'Trạng thái', 'Various status values used in system', 'Các giá trị trạng thái sử dụng trong hệ thống', true),
('PAYMENT_METHODS', 'Payment Methods', 'Payment Methods', 'Phương thức Thanh toán', 'Available payment methods', 'Các phương thức thanh toán có sẵn', true)
ON CONFLICT (category_id) DO NOTHING;

-- 2. Insert SPECIALTIES
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('SPEC001', 'SPECIALTIES', 'internal_medicine', 'Internal Medicine', 'Nội tổng hợp', 'General internal medicine', 'Nội khoa tổng hợp', 1, '#3B82F6', 'heart', true),
('SPEC002', 'SPECIALTIES', 'surgery', 'Surgery', 'Ngoại tổng hợp', 'General surgery', 'Ngoại khoa tổng hợp', 2, '#EF4444', 'scissors', true),
('SPEC003', 'SPECIALTIES', 'pediatrics', 'Pediatrics', 'Nhi khoa', 'Children medicine', 'Y học trẻ em', 3, '#10B981', 'baby', true),
('SPEC004', 'SPECIALTIES', 'obstetrics_gynecology', 'Obstetrics & Gynecology', 'Sản phụ khoa', 'Women health and childbirth', 'Sức khỏe phụ nữ và sinh nở', 4, '#F59E0B', 'female', true),
('SPEC005', 'SPECIALTIES', 'cardiology', 'Cardiology', 'Tim mạch', 'Heart and cardiovascular', 'Tim mạch và hệ tuần hoàn', 5, '#DC2626', 'heart-pulse', true),
('SPEC006', 'SPECIALTIES', 'neurology', 'Neurology', 'Thần kinh', 'Nervous system', 'Hệ thần kinh', 6, '#7C3AED', 'brain', true),
('SPEC007', 'SPECIALTIES', 'orthopedics', 'Orthopedics', 'Chấn thương chỉnh hình', 'Bone and joint surgery', 'Phẫu thuật xương khớp', 7, '#059669', 'bone', true),
('SPEC008', 'SPECIALTIES', 'emergency', 'Emergency Medicine', 'Cấp cứu', 'Emergency and critical care', 'Cấp cứu và hồi sức', 8, '#DC2626', 'ambulance', true),
('SPEC009', 'SPECIALTIES', 'dermatology', 'Dermatology', 'Da liễu', 'Skin conditions', 'Bệnh lý da', 9, '#F97316', 'skin', true),
('SPEC010', 'SPECIALTIES', 'ophthalmology', 'Ophthalmology', 'Mắt', 'Eye care', 'Chăm sóc mắt', 10, '#06B6D4', 'eye', true),
('SPEC011', 'SPECIALTIES', 'ent', 'ENT (Ear, Nose, Throat)', 'Tai mũi họng', 'Ear, nose and throat', 'Tai, mũi, họng', 11, '#8B5CF6', 'ear', true),
('SPEC012', 'SPECIALTIES', 'dentistry', 'Dentistry', 'Răng hàm mặt', 'Dental and oral care', 'Chăm sóc răng miệng', 12, '#14B8A6', 'tooth', true)
ON CONFLICT (enum_id) DO NOTHING;

-- 3. Insert DEPARTMENTS
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('DEPT001', 'DEPARTMENTS', 'emergency', 'Emergency Department', 'Khoa Cấp cứu', 'Emergency and trauma care', 'Cấp cứu và chấn thương', 1, '#DC2626', 'ambulance', true),
('DEPT002', 'DEPARTMENTS', 'internal_medicine', 'Internal Medicine', 'Khoa Nội', 'Internal medicine department', 'Khoa nội tổng hợp', 2, '#3B82F6', 'stethoscope', true),
('DEPT003', 'DEPARTMENTS', 'surgery', 'Surgery Department', 'Khoa Ngoại', 'Surgical department', 'Khoa phẫu thuật', 3, '#EF4444', 'scissors', true),
('DEPT004', 'DEPARTMENTS', 'pediatrics', 'Pediatrics', 'Khoa Nhi', 'Children department', 'Khoa trẻ em', 4, '#10B981', 'baby', true),
('DEPT005', 'DEPARTMENTS', 'obstetrics', 'Obstetrics & Gynecology', 'Khoa Sản', 'Maternity department', 'Khoa sản phụ', 5, '#F59E0B', 'female', true),
('DEPT006', 'DEPARTMENTS', 'cardiology', 'Cardiology', 'Khoa Tim mạch', 'Heart department', 'Khoa tim mạch', 6, '#DC2626', 'heart', true),
('DEPT007', 'DEPARTMENTS', 'neurology', 'Neurology', 'Khoa Thần kinh', 'Neurology department', 'Khoa thần kinh', 7, '#7C3AED', 'brain', true),
('DEPT008', 'DEPARTMENTS', 'orthopedics', 'Orthopedics', 'Khoa Chấn thương', 'Orthopedic department', 'Khoa chấn thương chỉnh hình', 8, '#059669', 'bone', true),
('DEPT009', 'DEPARTMENTS', 'radiology', 'Radiology', 'Khoa Chẩn đoán hình ảnh', 'Medical imaging', 'Chẩn đoán hình ảnh y khoa', 9, '#6366F1', 'scan', true),
('DEPT010', 'DEPARTMENTS', 'laboratory', 'Laboratory', 'Khoa Xét nghiệm', 'Medical laboratory', 'Xét nghiệm y khoa', 10, '#8B5CF6', 'flask', true),
('DEPT011', 'DEPARTMENTS', 'pharmacy', 'Pharmacy', 'Khoa Dược', 'Hospital pharmacy', 'Dược viện bệnh viện', 11, '#059669', 'pill', true),
('DEPT012', 'DEPARTMENTS', 'administration', 'Administration', 'Khoa Hành chính', 'Hospital administration', 'Hành chính bệnh viện', 12, '#6B7280', 'building', true)
ON CONFLICT (enum_id) DO NOTHING;

-- 4. Insert ROOM_TYPES
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('ROOM001', 'ROOM_TYPES', 'consultation', 'Consultation Room', 'Phòng khám', 'Doctor consultation room', 'Phòng khám bác sĩ', 1, '#3B82F6', 'stethoscope', true),
('ROOM002', 'ROOM_TYPES', 'surgery', 'Surgery Room', 'Phòng mổ', 'Operating theater', 'Phòng phẫu thuật', 2, '#EF4444', 'scissors', true),
('ROOM003', 'ROOM_TYPES', 'emergency', 'Emergency Room', 'Phòng cấp cứu', 'Emergency treatment room', 'Phòng điều trị cấp cứu', 3, '#DC2626', 'ambulance', true),
('ROOM004', 'ROOM_TYPES', 'ward', 'Ward Room', 'Phòng bệnh', 'Patient ward room', 'Phòng nằm bệnh nhân', 4, '#10B981', 'bed', true),
('ROOM005', 'ROOM_TYPES', 'icu', 'ICU', 'Phòng hồi sức', 'Intensive care unit', 'Đơn vị chăm sóc đặc biệt', 5, '#7C2D12', 'heart-pulse', true),
('ROOM006', 'ROOM_TYPES', 'laboratory', 'Laboratory', 'Phòng xét nghiệm', 'Medical laboratory', 'Phòng xét nghiệm y khoa', 6, '#8B5CF6', 'flask', true),
('ROOM007', 'ROOM_TYPES', 'radiology', 'Radiology Room', 'Phòng chụp', 'Medical imaging room', 'Phòng chẩn đoán hình ảnh', 7, '#6366F1', 'scan', true),
('ROOM008', 'ROOM_TYPES', 'pharmacy', 'Pharmacy', 'Phòng thuốc', 'Hospital pharmacy', 'Phòng thuốc bệnh viện', 8, '#059669', 'pill', true),
('ROOM009', 'ROOM_TYPES', 'waiting', 'Waiting Room', 'Phòng chờ', 'Patient waiting area', 'Khu vực chờ bệnh nhân', 9, '#6B7280', 'clock', true),
('ROOM010', 'ROOM_TYPES', 'meeting', 'Meeting Room', 'Phòng họp', 'Conference and meeting room', 'Phòng họp và hội nghị', 10, '#F59E0B', 'users', true)
ON CONFLICT (enum_id) DO NOTHING;

-- 5. Insert DIAGNOSIS (Common medical conditions)
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('DIAG001', 'DIAGNOSIS', 'hypertension', 'Hypertension', 'Tăng huyết áp', 'High blood pressure', 'Huyết áp cao', 1, '#DC2626', 'heart', true),
('DIAG002', 'DIAGNOSIS', 'diabetes', 'Diabetes Mellitus', 'Đái tháo đường', 'Diabetes type 1 and 2', 'Bệnh đái tháo đường', 2, '#F59E0B', 'droplet', true),
('DIAG003', 'DIAGNOSIS', 'pneumonia', 'Pneumonia', 'Viêm phổi', 'Lung infection', 'Nhiễm trùng phổi', 3, '#3B82F6', 'lungs', true),
('DIAG004', 'DIAGNOSIS', 'gastritis', 'Gastritis', 'Viêm dạ dày', 'Stomach inflammation', 'Viêm niêm mạc dạ dày', 4, '#10B981', 'stomach', true),
('DIAG005', 'DIAGNOSIS', 'migraine', 'Migraine', 'Đau nửa đầu', 'Severe headache', 'Đau đầu dữ dội', 5, '#7C3AED', 'brain', true),
('DIAG006', 'DIAGNOSIS', 'fracture', 'Bone Fracture', 'Gãy xương', 'Broken bone', 'Xương bị gãy', 6, '#059669', 'bone', true),
('DIAG007', 'DIAGNOSIS', 'appendicitis', 'Appendicitis', 'Viêm ruột thừa', 'Appendix inflammation', 'Viêm ruột thừa', 7, '#EF4444', 'alert-triangle', true),
('DIAG008', 'DIAGNOSIS', 'asthma', 'Asthma', 'Hen suyễn', 'Breathing difficulty', 'Khó thở', 8, '#06B6D4', 'wind', true),
('DIAG009', 'DIAGNOSIS', 'depression', 'Depression', 'Trầm cảm', 'Mental health condition', 'Tình trạng sức khỏe tâm thần', 9, '#8B5CF6', 'brain', true),
('DIAG010', 'DIAGNOSIS', 'covid19', 'COVID-19', 'COVID-19', 'Coronavirus disease', 'Bệnh do virus corona', 10, '#DC2626', 'virus', true)
ON CONFLICT (enum_id) DO NOTHING;

-- 6. Insert MEDICATIONS (Common medication categories)
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('MED001', 'MEDICATIONS', 'antibiotics', 'Antibiotics', 'Kháng sinh', 'Bacterial infection treatment', 'Điều trị nhiễm trùng vi khuẩn', 1, '#EF4444', 'pill', true),
('MED002', 'MEDICATIONS', 'analgesics', 'Analgesics', 'Thuốc giảm đau', 'Pain relief medication', 'Thuốc giảm đau', 2, '#F59E0B', 'zap-off', true),
('MED003', 'MEDICATIONS', 'antihypertensives', 'Antihypertensives', 'Thuốc hạ huyết áp', 'Blood pressure medication', 'Thuốc điều trị huyết áp', 3, '#DC2626', 'heart', true),
('MED004', 'MEDICATIONS', 'antidiabetics', 'Antidiabetics', 'Thuốc tiểu đường', 'Diabetes medication', 'Thuốc điều trị tiểu đường', 4, '#10B981', 'droplet', true),
('MED005', 'MEDICATIONS', 'antihistamines', 'Antihistamines', 'Thuốc kháng histamine', 'Allergy medication', 'Thuốc điều trị dị ứng', 5, '#06B6D4', 'shield', true),
('MED006', 'MEDICATIONS', 'vitamins', 'Vitamins', 'Vitamin', 'Nutritional supplements', 'Thực phẩm bổ sung dinh dưỡng', 6, '#059669', 'apple', true),
('MED007', 'MEDICATIONS', 'sedatives', 'Sedatives', 'Thuốc an thần', 'Calming medication', 'Thuốc làm dịu', 7, '#7C3AED', 'moon', true),
('MED008', 'MEDICATIONS', 'vaccines', 'Vaccines', 'Vaccine', 'Immunization', 'Tiêm chủng', 8, '#3B82F6', 'syringe', true),
('MED009', 'MEDICATIONS', 'topical', 'Topical Medications', 'Thuốc bôi ngoài da', 'External application', 'Thuốc sử dụng ngoài da', 9, '#F97316', 'droplets', true),
('MED010', 'MEDICATIONS', 'emergency', 'Emergency Medications', 'Thuốc cấp cứu', 'Emergency treatment drugs', 'Thuốc điều trị cấp cứu', 10, '#DC2626', 'ambulance', true)
ON CONFLICT (enum_id) DO NOTHING;

-- 7. Insert STATUS_VALUES (Various system statuses)
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('STAT001', 'STATUS_VALUES', 'active', 'Active', 'Hoạt động', 'Currently active', 'Đang hoạt động', 1, '#10B981', 'check-circle', true),
('STAT002', 'STATUS_VALUES', 'inactive', 'Inactive', 'Không hoạt động', 'Currently inactive', 'Không hoạt động', 2, '#6B7280', 'x-circle', true),
('STAT003', 'STATUS_VALUES', 'pending', 'Pending', 'Chờ xử lý', 'Waiting for action', 'Đang chờ xử lý', 3, '#F59E0B', 'clock', true),
('STAT004', 'STATUS_VALUES', 'completed', 'Completed', 'Hoàn thành', 'Successfully completed', 'Đã hoàn thành', 4, '#059669', 'check-circle-2', true),
('STAT005', 'STATUS_VALUES', 'cancelled', 'Cancelled', 'Đã hủy', 'Cancelled or terminated', 'Đã bị hủy bỏ', 5, '#EF4444', 'x-circle', true),
('STAT006', 'STATUS_VALUES', 'scheduled', 'Scheduled', 'Đã lên lịch', 'Scheduled for future', 'Đã được lên lịch', 6, '#3B82F6', 'calendar', true),
('STAT007', 'STATUS_VALUES', 'in_progress', 'In Progress', 'Đang thực hiện', 'Currently in progress', 'Đang được thực hiện', 7, '#F59E0B', 'loader', true),
('STAT008', 'STATUS_VALUES', 'on_hold', 'On Hold', 'Tạm dừng', 'Temporarily paused', 'Tạm thời dừng lại', 8, '#8B5CF6', 'pause-circle', true),
('STAT009', 'STATUS_VALUES', 'expired', 'Expired', 'Hết hạn', 'Past expiration date', 'Đã hết hạn', 9, '#DC2626', 'calendar-x', true),
('STAT010', 'STATUS_VALUES', 'archived', 'Archived', 'Lưu trữ', 'Moved to archive', 'Đã chuyển vào lưu trữ', 10, '#6B7280', 'archive', true)
ON CONFLICT (enum_id) DO NOTHING;

-- 8. Insert PAYMENT_METHODS
INSERT INTO system_enums (enum_id, category_id, enum_key, display_name_en, display_name_vi, description_en, description_vi, sort_order, color_code, icon_name, is_system) VALUES
('PAY001', 'PAYMENT_METHODS', 'cash', 'Cash', 'Tiền mặt', 'Cash payment', 'Thanh toán bằng tiền mặt', 1, '#10B981', 'banknote', true),
('PAY002', 'PAYMENT_METHODS', 'credit_card', 'Credit Card', 'Thẻ tín dụng', 'Credit card payment', 'Thanh toán bằng thẻ tín dụng', 2, '#3B82F6', 'credit-card', true),
('PAY003', 'PAYMENT_METHODS', 'debit_card', 'Debit Card', 'Thẻ ghi nợ', 'Debit card payment', 'Thanh toán bằng thẻ ghi nợ', 3, '#059669', 'credit-card', true),
('PAY004', 'PAYMENT_METHODS', 'bank_transfer', 'Bank Transfer', 'Chuyển khoản', 'Electronic bank transfer', 'Chuyển khoản ngân hàng', 4, '#6366F1', 'building-2', true),
('PAY005', 'PAYMENT_METHODS', 'mobile_payment', 'Mobile Payment', 'Thanh toán di động', 'Mobile app payment', 'Thanh toán qua ứng dụng di động', 5, '#F59E0B', 'smartphone', true),
('PAY006', 'PAYMENT_METHODS', 'insurance', 'Insurance', 'Bảo hiểm', 'Insurance coverage', 'Thanh toán qua bảo hiểm', 6, '#7C3AED', 'shield-check', true),
('PAY007', 'PAYMENT_METHODS', 'installment', 'Installment', 'Trả góp', 'Payment in installments', 'Thanh toán theo đợt', 7, '#F97316', 'calendar-days', true),
('PAY008', 'PAYMENT_METHODS', 'voucher', 'Voucher', 'Phiếu giảm giá', 'Discount voucher', 'Phiếu ưu đãi', 8, '#EC4899', 'ticket', true),
('PAY009', 'PAYMENT_METHODS', 'company_account', 'Company Account', 'Tài khoản công ty', 'Corporate account payment', 'Thanh toán tài khoản doanh nghiệp', 9, '#8B5CF6', 'building', true),
('PAY010', 'PAYMENT_METHODS', 'government_subsidy', 'Government Subsidy', 'Trợ cấp nhà nước', 'Government financial support', 'Hỗ trợ tài chính từ nhà nước', 10, '#DC2626', 'landmark', true)
ON CONFLICT (enum_id) DO NOTHING;
